import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "../ui/accordion";

const Faq = () => {
  const faqs = [
    {
      question: "What facial factors does the AI analyze for looksmax improvement?",
      answer:
        "Our AI analyzes 9 key factors prioritized by looksmax principles: facial symmetry, jawlines, cheekbones, face shape, eyes, eyebrow shape, skin quality, masculinity, and hairstyle. The analysis focuses on bone structure (highest priority), improvable features (medium priority), and styling choices (lower priority) to give you actionable insights.",
    },
    {
      question: "Is my photo data kept private and secure?",
      answer:
        "Absolutely. Your privacy is our top priority. All uploaded images are processed in real-time and immediately deleted after analysis. We never store your photos, create profiles, or share your data with third parties. The analysis is completely anonymous and secure.",
    },
    {
      question: "How accurate and scientific is the facial analysis?",
      answer:
        "Our AI uses computer vision technology trained on scientific beauty research, including golden ratio proportions, facial symmetry studies, and established attractiveness markers. While beauty is subjective, our analysis provides objective measurements based on peer-reviewed research to help you understand your facial characteristics.",
    },
    {
      question: "Can this analysis actually help me improve my appearance?",
      answer:
        "Yes! The analysis provides practical, natural improvement suggestions based on your specific features. You'll get personalized tips for skincare, grooming, photography angles, hairstyles, and other achievable enhancements. We focus on maximizing your natural potential without suggesting surgery or extreme measures.",
    },
    {
      question: "What makes this different from other rating apps?",
      answer:
        "Unlike simple rating apps, we provide detailed factor-by-factor analysis with specific improvement recommendations. Our looksmax-focused approach prioritizes bone structure analysis while giving actionable advice for features you can actually improve. Plus, we maintain complete privacy with no data storage.",
    },
  ];

  return (
    <section id="faq" className="py-32 bg-muted/30">
      <div className="container">
        <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl text-center">
          Frequently Asked Questions
        </h2>
        {faqs.map((faq, index) => (
          <Accordion key={index} type="single" collapsible>
            <AccordionItem value={`item-${index}`}>
              <AccordionTrigger className="hover:text-foreground/60 hover:no-underline text-lg">
                {faq.question}
              </AccordionTrigger>
              <AccordionContent className="text-normal">
                {faq.answer}
              </AccordionContent>
            </AccordionItem>
          </Accordion>
        ))}
      </div>
    </section>
  );
};

export default Faq;
