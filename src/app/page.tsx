"use client";

import { <PERSON><PERSON> } from "../components/ui/button";
import Testimonials from "../components/landing-page/testimonials";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "../components/ui/accordion";
import { Star, Camera, Sparkles, Shield } from 'lucide-react';
import Navbar from "../components/landing-page/navbar";
import Contact from "../components/landing-page/contact";
import Hero from "../components/landing-page/hero";
import HowToUse from "../components/landing-page/how-to-use";
import Faq from "../components/landing-page/faq";
import Image from "next/image";
import Link from "next/link";
import { SignInButton, SignUpButton, useUser } from "@clerk/nextjs";

// Rest of the component remains the same
export default function Home() {
  const { isSignedIn } = useUser();

  return (
    <div className="flex min-h-screen flex-col">
      <Navbar />

      <Hero />

      {/* Features Section */}
      <section className="py-12 bg-background" aria-labelledby="features-heading">
        <div className="container mx-auto px-4">
          <h2 id="features-heading" className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl text-center mb-8 text-foreground">Why Choose Our AI Analysis?</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {/* Feature 1 */}
            <article className="text-center p-6 bg-card rounded-lg shadow-md">
              <h3 className="text-xl font-semibold mb-4 text-foreground">AI-Powered Analysis</h3>
              <p className="text-muted-foreground">
                Advanced computer vision algorithms analyze facial features with scientific precision and accuracy.
              </p>
            </article>

            {/* Feature 2 */}
            <article className="text-center p-6 bg-card rounded-lg shadow-md">
              <h3 className="text-xl font-semibold mb-4 text-foreground">Instant Results</h3>
              <p className="text-muted-foreground">
                Get detailed analysis results in seconds with comprehensive scoring and recommendations.
              </p>
            </article>

            {/* Feature 3 */}
            <article className="text-center p-6 bg-card rounded-lg shadow-md">
              <h3 className="text-xl font-semibold mb-4 text-foreground">Privacy First</h3>
              <p className="text-muted-foreground">
                Your photos are processed securely and never stored. Complete privacy guaranteed.
              </p>
            </article>
          </div>
        </div>
      </section>

      <HowToUse />

      <Testimonials />

      {/* Pricing section commented out for future implementation */}
      {/* <section className="container py-24 sm:py-32" id="pricing"> */}
      {/*   <div className="mx-auto flex max-w-2xl flex-col items-center gap-4 text-center"> */}
      {/*     <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl"> */}
      {/*       Simple, transparent pricing */}
      {/*     </h2> */}
      {/*     <p className="max-w-[600px] text-muted-foreground md:text-xl"> */}
      {/*       Choose the plan that works best for you */}
      {/*     </p> */}
      {/*   </div> */}
      {/*   <div className="mx-auto grid max-w-5xl gap-8 md:grid-cols-3 lg:gap-12 mt-12"> */}
      {/*     { */}
      {/*       [ */}
      {/*         { */}
      {/*           title: "Free", */}
      {/*           price: "$0", */}
      {/*           features: ["3 photo analyses per month", "Basic style tips", "Community access"], */}
      {/*         }, */}
      {/*         { */}
      {/*           title: "Pro", */}
      {/*           price: "$9.99", */}
      {/*           features: ["Unlimited analyses", "Detailed feedback", "Style recommendations", "Priority support"], */}
      {/*         }, */}
      {/*         { */}
      {/*           title: "Premium", */}
      {/*           price: "$19.99", */}
      {/*           features: ["Everything in Pro", "Personal AI stylist", "Trend predictions", "VIP support"], */}
      {/*         }, */}
      {/*       ].map((plan) => ( */}
      {/*         <div key={plan.title} className="flex flex-col gap-4 rounded-lg border p-6"> */}
      {/*           <div> */}
      {/*             <h3 className="font-bold">{plan.title}</h3> */}
      {/*             <p className="text-2xl font-bold">{plan.price}</p> */}
      {/*             <p className="text-sm text-muted-foreground">per month</p> */}
      {/*           </div> */}
      {/*           <ul className="flex flex-col gap-2"> */}
      {/*             {plan.features.map((feature) => ( */}
      {/*               <li key={feature} className="flex items-center gap-2"> */}
      {/*                 <Shield className="h-4 w-4 text-green-500" /> */}
      {/*                 <span className="text-sm">{feature}</span> */}
      {/*               </li> */}
      {/*             ))} */}
      {/*           </ul> */}
      {/*           <SignUpButton> */}
      {/*             <Button className="mt-auto w-full" variant={plan.title === "Pro" ? "default" : "outline"}> */}
      {/*               Get Started */}
      {/*             </Button> */}
      {/*           </SignUpButton> */}
      {/*         </div> */}
      {/*       ))} */}
      {/*   </div> */}
      {/* </section> */}

      {/* FAQ */}
      <Faq />

      {/* Contact */}
      <Contact />

      <footer className="border-t py-12">
        <div className="container flex flex-col items-center gap-4 text-center">
          <div className="flex items-center gap-2 font-bold">
            <Camera className="h-6 w-6" />
            <span>Attractiveness Score</span>
          </div>
          <div className="flex gap-4 text-sm text-muted-foreground">
            <Link href="/privacy-policy" className="hover:underline">
              Privacy Policy
            </Link>
            <Link href="/terms-of-service" className="hover:underline">
              Terms of Service
            </Link>
          </div>
          <p className="text-sm text-muted-foreground">
            © {new Date().getFullYear()} Attractiveness Score. All rights reserved.
          </p>
        </div>
      </footer>
    </div>
  );
}
